{% block brainst_odoo_list %}

    <sw-page class="brainst-odoo-list sw-settings-index">
        {% block brainst_odoo_list_smart_bar_header %}
            <template #smart-bar-header>
                {% block brainst_odoo_list_smart_bar_header_title %}
                    <h2>
                        {% block brainst_odoo_list_smart_bar_header_title_text %}
                            {{ $tc('brainst-odoo-pro.list.title') }}
                        {% endblock %}
                    </h2>
                {% endblock %}
            </template>
            <template #smart-bar-actions>
                {% block brainst_odoo_list_smart_bar_action %}
                    <brainst-odoo-pro-synchronize-button/>
                {% endblock %}
            </template>
        {% endblock %}

        {% block brainst_odoo_list_content %}
            <template #content>
                {% block brainst_odoo_list_content_card_view %}
                    <sw-card-view>
                        <!-- Dashboard Overview Card -->
                        <sw-card class="brainst-odoo-dashboard__card" positionIdentifier="brainst-odoo-dashboard-card">
                            <div class="brainst-odoo-dashboard">
                                <div class="dashboard-header">
                                    <div class="dashboard-title-section">
                                        <h3>{{ $tc('brainst-odoo-pro.dashboard.title') }}</h3>
                                        <sw-button
                                            variant="ghost"
                                            size="small"
                                            @click="getAllData"
                                            :disabled="isLoading"
                                            class="dashboard-refresh-btn"
                                        >
                                            <sw-icon name="regular-redo" small></sw-icon>
                                            {{ $tc('brainst-odoo-pro.dashboard.refresh') }}
                                        </sw-button>
                                    </div>
                                    <div class="dashboard-stats">
                                        <div class="stat-item">
                                            <span class="stat-value">{{ syncedCount }}</span>
                                            <span class="stat-label">{{ $tc('brainst-odoo-pro.dashboard.totalSynced') }}</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-value">{{ Object.keys(entityCounts).length }}</span>
                                            <span class="stat-label">{{ $tc('brainst-odoo-pro.dashboard.entityTypes') }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="dashboard-progress">
                                    <brainst-odoo-pro-progress-bar
                                        :value="syncedCount"
                                        :totalRecords="totalRecords"
                                        :animated="true"
                                    />
                                </div>

                                <!-- Sync Rate Gauge Chart -->
                                <div class="gauge-chart-container">
                                    <div class="gauge-header">
                                        <h4>{{ $tc('brainst-odoo-pro.dashboard.gauge.title') }}</h4>
                                        <div class="gauge-controls">
                                            <div class="gauge-time-controls">
                                                <sw-button
                                                    :variant="selectedTimePeriod === 'hour' ? 'primary' : 'ghost'"
                                                    size="small"
                                                    @click="switchTimePeriod('hour')"
                                                    :title="$tc('brainst-odoo-pro.dashboard.gauge.switchToHour')"
                                                    class="gauge-control-btn"
                                                >
                                                    {{ $tc('brainst-odoo-pro.dashboard.gauge.lastHour') }}
                                                </sw-button>
                                                <sw-button
                                                    :variant="selectedTimePeriod === 'minute' ? 'primary' : 'ghost'"
                                                    size="small"
                                                    @click="switchTimePeriod('minute')"
                                                    :title="$tc('brainst-odoo-pro.dashboard.gauge.switchToMinute')"
                                                    class="gauge-control-btn"
                                                >
                                                    {{ $tc('brainst-odoo-pro.dashboard.gauge.lastMinute') }}
                                                </sw-button>
                                            </div>
                                            <sw-button
                                                variant="ghost"
                                                size="small"
                                                @click="refreshSyncRate"
                                                :disabled="isLoading"
                                                class="gauge-refresh-btn"
                                                :title="$tc('brainst-odoo-pro.dashboard.refresh')"
                                            >
                                                <sw-icon name="regular-redo" small></sw-icon>
                                            </sw-button>
                                        </div>
                                    </div>

                                    <!-- Gauge Chart Component -->
                                    <brainst-odoo-pro-gauge-chart
                                        :value="currentSyncRate.value"
                                        :max-value="currentSyncRate.max"
                                        :title="currentSyncRate.label"
                                        :unit="currentSyncRate.unit"
                                        :display-value="currentSyncRate.displayValue"
                                        :is-loading="isLoading"
                                    />
                                </div>

                                <!-- Tables List Section -->
                                <div class="dashboard-tables-section">
                                    <h4>{{ $tc('brainst-odoo-pro.list.title') }}</h4>
                                    <div class="sw-settings__content-grid">
                                        <template v-for="odooTable in odooTables" :key="odooTable.path">
                                            <div class="brainst-odoo-table-item"
                                                 :class="'status-' + getEntitySyncStatus(odooTable.entityKey)"
                                                 :title="getEntityTooltip(odooTable.entityKey)"
                                                 @click="$router.push({ name: odooTable.path })"
                                                 v-tooltip="getEntityTooltip(odooTable.entityKey)">
                                                <div class="table-item-header">
                                                    <div class="table-item-icon"
                                                         :style="{ backgroundColor: odooTable.color }">
                                                        <sw-icon :name="getEntityIcon(odooTable.icon)" size="20px"></sw-icon>
                                                    </div>
                                                    <div class="table-item-status">
                                                        <sw-label
                                                            :variant="getStatusBadgeVariant(getEntitySyncStatus(odooTable.entityKey))"
                                                            size="small"
                                                            class="status-badge">
                                                            {{ getEntityStats(odooTable.entityKey).percentage }}%
                                                        </sw-label>
                                                    </div>
                                                </div>
                                                <div class="table-item-content">
                                                    <h4 class="table-item-title">{{ odooTable.title }}</h4>
                                                    <div class="table-item-stats" v-if="!isLoading">
                                                        <div class="stat-row">
                                                            <span class="stat-label">{{ $tc('brainst-odoo-pro.dashboard.synced') }}:</span>
                                                            <span class="stat-value synced">{{ getEntityStats(odooTable.entityKey).synced }}</span>
                                                        </div>
                                                        <div class="stat-row">
                                                            <span class="stat-label">{{ $tc('brainst-odoo-pro.dashboard.total') }}:</span>
                                                            <span class="stat-value total">{{ getEntityStats(odooTable.entityKey).total }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="table-item-progress" v-if="!isLoading">
                                                        <div class="progress-bar">
                                                            <div class="progress-fill"
                                                                 :style="{
                                                                     width: getEntityStats(odooTable.entityKey).percentage + '%',
                                                                     backgroundColor: odooTable.color
                                                                 }"></div>
                                                        </div>
                                                    </div>
                                                    <div class="table-item-loading" v-if="isLoading">
                                                        <sw-loader size="16px"></sw-loader>
                                                    </div>
                                                </div>
                                                <div class="table-item-arrow">
                                                    <sw-icon name="regular-chevron-right" size="12px"></sw-icon>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>

                                <!-- Entity Synced Bar Chart - Full Width -->
                                <div class="chart-container chart-container-full-width">
                                    <h4>{{ $tc('brainst-odoo-pro.dashboard.entitySynced') }}</h4>
                                    <div class="bar-chart" v-if="!isLoading">
                                        <div
                                            v-for="item in chartData"
                                            :key="item.name"
                                            class="bar-item"
                                        >
                                            <div class="bar-label">{{ item.name }}</div>
                                            <div class="bar-wrapper">
                                                <div
                                                    class="bar-fill"
                                                    :style="{
                                                        width: (item.synced / item.value) * 100 + '%',
                                                        backgroundColor: item.color
                                                    }"
                                                ></div>
                                                <span class="bar-value">{{ item.synced }}/{{ item.value }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Charts Grid -->
                                <div class="charts-grid">

                                    <!-- Sync Status Donut Chart -->
                                    <div class="chart-container donut-chart-container">
                                        <h4>{{ $tc('brainst-odoo-pro.dashboard.syncStatus') }}</h4>
                                        <div class="donut-chart" v-if="!isLoading">
                                            <div class="donut-svg-wrapper">
                                                <svg viewBox="0 0 100 100" class="donut-svg">
                                                    <circle
                                                        v-for="(item, index) in syncStatusData"
                                                        :key="item.name"
                                                        cx="50"
                                                        cy="50"
                                                        r="35"
                                                        :stroke="item.color"
                                                        stroke-width="12"
                                                        fill="transparent"
                                                        :stroke-dasharray="(item.value / (syncStatusData[0].value + syncStatusData[1].value)) * 220 + ' 220'"
                                                        :stroke-dashoffset="index === 0 ? 0 : -(syncStatusData[0].value / (syncStatusData[0].value + syncStatusData[1].value)) * 220"
                                                        class="donut-segment"
                                                    />
                                                </svg>
                                                <div class="donut-center">
                                                    <span class="donut-percentage">
                                                        {{ Math.round((syncStatusData[0].value / (syncStatusData[0].value + syncStatusData[1].value)) * 100) }}%
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="donut-legend">
                                                <div
                                                    v-for="item in syncStatusData"
                                                    :key="item.name"
                                                    class="legend-item"
                                                >
                                                    <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
                                                    <span class="legend-text">{{ item.name }}: {{ item.value }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Progress Ring Chart -->
                                    <div class="chart-container progress-chart-container">
                                        <h4>{{ $tc('brainst-odoo-pro.dashboard.overallProgress') }}</h4>
                                        <div class="progress-pie" v-if="!isLoading">
                                            <div class="pie-svg-wrapper">
                                                <svg viewBox="0 0 100 100" class="pie-svg">
                                                    <!-- Background circle -->
                                                    <circle
                                                        cx="50"
                                                        cy="50"
                                                        r="40"
                                                        fill="#f3f4f6"
                                                        class="pie-background"
                                                    />
                                                    <!-- Synced segment -->
                                                    <path
                                                        :d="getSyncedPath()"
                                                        fill="#10b981"
                                                        class="pie-segment pie-segment-synced"
                                                        @mouseenter="showHover('synced')"
                                                        @mouseleave="hideHover"
                                                    />
                                                    <!-- Pending segment -->
                                                    <path
                                                        :d="getPendingPath()"
                                                        fill="#f59e0b"
                                                        class="pie-segment pie-segment-pending"
                                                        @mouseenter="showHover('pending')"
                                                        @mouseleave="hideHover"
                                                    />
                                                </svg>
                                                <div class="pie-center">
                                                    <span class="pie-percentage" v-show="!hoverInfo.show">
                                                        {{ totalRecords > 0 ? Math.round((syncedCount / totalRecords) * 100) : 0 }}%
                                                    </span>
                                                    <div class="pie-hover-info" v-show="hoverInfo.show">
                                                        <span class="pie-hover-label">{{ hoverInfo.label }}</span>
                                                        <span class="pie-hover-percentage">{{ hoverInfo.percentage }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="progress-legend">
                                                <div class="progress-stats">
                                                    <div class="progress-stat-item">
                                                        <span class="progress-stat-color" style="background-color: #10b981;"></span>
                                                        <span class="progress-stat-text">
                                                            {{ $tc('brainst-odoo-pro.dashboard.synced') }}: {{ totalRecords > 0 ? Math.round((syncedCount / totalRecords) * 100) : 0 }}%
                                                        </span>
                                                    </div>
                                                    <div class="progress-stat-item">
                                                        <span class="progress-stat-color" style="background-color: #f59e0b;"></span>
                                                        <span class="progress-stat-text">
                                                            {{ $tc('brainst-odoo-pro.dashboard.pending') }}: {{ totalRecords > 0 ? Math.round(((totalRecords - syncedCount) / totalRecords) * 100) : 0 }}%
                                                        </span>
                                                    </div>
                                                    <div class="progress-stat-item">
                                                        <span class="progress-stat-color" style="background-color: #6b7280;"></span>
                                                        <span class="progress-stat-text">
                                                            {{ $tc('brainst-odoo-pro.dashboard.total') }}: {{ totalRecords }} {{ $tc('brainst-odoo-pro.dashboard.records') }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </sw-card>
                    </sw-card-view>
                {% endblock %}
            </template>
        {% endblock %}
    </sw-page>
{% endblock %}
