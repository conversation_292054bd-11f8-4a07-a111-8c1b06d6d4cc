{"name": "shopware/production", "license": "MIT", "type": "project", "require": {"ext-simplexml": "*", "composer-runtime-api": "^2.0", "brainst/odoo": "2.2.1", "shopware/administration": "6.6.8.2", "shopware/conflicts": ">=0.3.0", "shopware/core": "6.6.8.2", "shopware/elasticsearch": "6.6.8.2", "shopware/storefront": "6.6.8.2", "symfony/flex": "~2", "symfony/runtime": ">=5"}, "repositories": [{"type": "path", "url": "custom/plugins/*", "options": {"symlink": true}}, {"type": "path", "url": "custom/plugins/*/packages/*", "options": {"symlink": true}}, {"type": "path", "url": "custom/static-plugins/*", "options": {"symlink": true}}], "prefer-stable": true, "config": {"allow-plugins": {"symfony/flex": true, "symfony/runtime": true, "composer/package-versions-deprecated": true}, "optimize-autoloader": true, "sort-packages": true}, "scripts": {"auto-scripts": {"assets:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "ps": "vendor/bin/phpstan analyse --configuration phpstan.neon"}, "extra": {"symfony": {"allow-contrib": true, "endpoint": ["https://raw.githubusercontent.com/shopware/recipes/flex/main/index.json", "flex://defaults"]}}, "require-dev": {"phpstan/phpstan": "^2.0", "phpunit/phpunit": "^11.4", "shopware/dev-tools": "^1.4", "symfony/stopwatch": "^7.0", "symfony/web-profiler-bundle": "^7.0"}}